# API Reference

## Core Classes

### BaseModel

Abstract base class for all model implementations.

```python
from llm_modeling_metrics import BaseModel, ModelMetrics, ParallelConfig

class BaseModel(ABC):
    def __init__(self, model_name: str, config: Optional[Any] = None)
```

#### Methods

##### `get_metrics(sequence_length: int = 2048, parallel_config: Optional[ParallelConfig] = None) -> ModelMetrics`

Get comprehensive model metrics including parameters, FLOPs, and memory requirements.

**Parameters:**
- `sequence_length` (int): Input sequence length for FLOP calculation
- `parallel_config` (ParallelConfig, optional): Parallel execution configuration

**Returns:**
- `ModelMetrics`: Comprehensive metrics object

**Example:**
```python
model = ModelFactory.create_model("meta-llama/Llama-2-7b-hf")
metrics = model.get_metrics(sequence_length=4096)
print(f"Parameters: {metrics.total_params:,}")
print(f"FLOPs: {metrics.flops_forward:,}")
```

##### `compute_flops(sequence_length: int = 2048) -> Dict[str, int]`

Compute detailed FLOP breakdown by component.

**Parameters:**
- `sequence_length` (int): Input sequence length

**Returns:**
- `Dict[str, int]`: FLOP breakdown by component

**Example:**
```python
flops = model.compute_flops(sequence_length=2048)
print(f"Attention FLOPs: {flops['attention']:,}")
print(f"MLP FLOPs: {flops['mlp']:,}")
```

##### `compute_memory_requirements(sequence_length: int = 2048, batch_size: int = 1, **kwargs) -> Dict[str, int]`

Compute detailed memory requirements with mixed precision support.

**Parameters:**
- `sequence_length` (int): Input sequence length
- `batch_size` (int): Batch size
- `dtype` (str, optional): Legacy parameter for uniform precision (default: 'bf16')
- `weight_dtype` (str, optional): Precision for model weights (default: 'bf16')
- `activation_dtype` (str, optional): Precision for activations (default: 'bf16')
- `kv_cache_dtype` (str, optional): Precision for KV cache (default: 'bf16')
- `attention_parameter_dtype` (str, optional): Precision for attention parameters (default: 'bf16')
- `grad_dtype` (str, optional): Precision for gradients during training (default: 'fp16')
- `optimizer_dtype` (str, optional): Precision for optimizer states (default: 'fp32')
- `training` (bool): Whether to include training memory (default: False)
- `include_kv_cache` (bool): Whether to include KV cache memory (default: False)

**Returns:**
- `Dict[str, int]`: Memory breakdown by component in bytes

**Mixed Precision Example:**
```python
# Basic mixed precision
memory = model.compute_memory_requirements(
    sequence_length=2048,
    weight_dtype='bf16',
    activation_dtype='bf16',
    kv_cache_dtype='fp8',  # Save memory on KV cache
    include_kv_cache=True
)

# Training with mixed precision
training_memory = model.compute_memory_requirements(
    sequence_length=2048,
    weight_dtype='bf16',
    activation_dtype='bf16',
    grad_dtype='fp16',      # FP16 gradients
    optimizer_dtype='fp32', # FP32 optimizer states
    training=True
)
```

**Supported Precision Types:**
- `'fp32'`: 32-bit floating point (4 bytes)
- `'bf16'`: 16-bit brain floating point (2 bytes)
- `'fp16'`: 16-bit floating point (2 bytes)
- `'fp8'`: 8-bit floating point (1 byte)
- `'int8'`: 8-bit integer (1 byte)
- `'fp4'`: 4-bit floating point (0.5 bytes, packed)

##### `get_matrix_shapes(parallel_config: ParallelConfig) -> Dict[str, Any]`

Get matrix shapes under parallel configuration.

**Parameters:**
- `parallel_config` (ParallelConfig): Parallel execution configuration

**Returns:**
- `Dict[str, Any]`: Matrix shapes by component

### ModelFactory

Factory class for creating model instances.

```python
from llm_modeling_metrics import ModelFactory

class ModelFactory:
    @classmethod
    def create_model(cls, model_name: str, config: Optional[Any] = None) -> BaseModel
```

#### Methods

##### `create_model(model_name: str, config: Optional[Any] = None) -> BaseModel`

Create appropriate model instance based on architecture.

**Parameters:**
- `model_name` (str): HuggingFace model name or path
- `config` (optional): Pre-loaded model configuration

**Returns:**
- `BaseModel`: Model instance

**Example:**
```python
# Create from HuggingFace model name
model = ModelFactory.create_model("meta-llama/Llama-2-7b-hf")

# Create with custom config
from transformers import AutoConfig
config = AutoConfig.from_pretrained("meta-llama/Llama-2-7b-hf")
model = ModelFactory.create_model("meta-llama/Llama-2-7b-hf", config=config)
```

##### `register_model(architecture: str, model_class: type)`

Register new model architecture.

**Parameters:**
- `architecture` (str): Architecture identifier
- `model_class` (type): Model class implementing BaseModel

**Example:**
```python
class CustomModel(BaseModel):
    # Implementation here
    pass

ModelFactory.register_model('custom', CustomModel)
model = ModelFactory.create_model("custom-model-name")
```

##### `get_supported_architectures() -> List[str]`

Get list of supported model architectures.

**Returns:**
- `List[str]`: List of architecture names

### ConfigManager

Manages model configuration fetching and caching.

```python
from llm_modeling_metrics import ConfigManager

class ConfigManager:
    def __init__(self, cache_dir: str = "~/.modeling_llm/model_configs", token: Optional[str] = None)
```

#### Methods

##### `fetch_config(model_name: str, force_refresh: bool = False) -> Dict[str, Any]`

Fetch and cache model configuration.

**Parameters:**
- `model_name` (str): HuggingFace model name
- `force_refresh` (bool): Force refresh from remote

**Returns:**
- `Dict[str, Any]`: Model configuration

**Example:**
```python
config_manager = ConfigManager(token="your_hf_token")
config = config_manager.fetch_config("meta-llama/Llama-2-7b-hf")
```

### ParallelStrategyCalculator

Utilities for parallel strategy analysis.

```python
from llm_modeling_metrics import ParallelStrategyCalculator, ParallelConfig

class ParallelStrategyCalculator:
    @staticmethod
    def compute_tensor_parallel_shapes(base_shapes: Dict[str, Tuple[int, ...]], tp_size: int) -> Dict[str, Tuple[int, ...]]
```

#### Methods

##### `compute_tensor_parallel_shapes(base_shapes: Dict, tp_size: int) -> Dict`

Compute matrix shapes under tensor parallelism.

**Parameters:**
- `base_shapes` (Dict): Original matrix shapes
- `tp_size` (int): Tensor parallel size

**Returns:**
- `Dict`: Modified shapes under parallelism

##### `validate_parallel_config(config: ParallelConfig, model_config: Dict) -> bool`

Validate parallel configuration feasibility.

**Parameters:**
- `config` (ParallelConfig): Parallel configuration
- `model_config` (Dict): Model configuration

**Returns:**
- `bool`: Whether configuration is valid

## Data Classes

### ModelMetrics

Container for model metrics.

```python
@dataclass
class ModelMetrics:
    total_params: int
    flops_forward: int
    flops_per_token: int
    memory_params: int
    memory_activations: int
    attention_shapes: Dict[str, Tuple[int, ...]]
    mlp_shapes: Dict[str, Tuple[int, ...]]
```

**Attributes:**
- `total_params`: Total model parameters
- `flops_forward`: Forward pass FLOPs
- `flops_per_token`: FLOPs per token
- `memory_params`: Parameter memory (bytes)
- `memory_activations`: Activation memory (bytes)
- `attention_shapes`: Attention layer matrix shapes
- `mlp_shapes`: MLP layer matrix shapes

### ParallelConfig

Configuration for parallel execution strategies.

```python
@dataclass
class ParallelConfig:
    tensor_parallel_size: int = 1
    pipeline_parallel_size: int = 1
    data_parallel_size: int = 1
```

**Attributes:**
- `tensor_parallel_size`: Tensor parallelism degree
- `pipeline_parallel_size`: Pipeline parallelism degree
- `data_parallel_size`: Data parallelism degree

## Model Implementations

The system automatically classifies all models into two types:

### DenseModel

Implementation for dense transformer models where all parameters are active for every token.

```python
from llm_modeling_metrics.models import DenseModel

model = DenseModel("meta-llama/Llama-2-7b-hf")
```

**Automatically detected for:**
- Llama family (Llama 2, Llama 3, Code Llama, etc.)
- Qwen family (Qwen, Qwen2, Qwen2.5, etc.)
- Mistral family (dense variants)
- Any standard transformer architecture without MoE components

### MoEModel

Implementation for Mixture of Experts models with sparse activation.

```python
from llm_modeling_metrics.models import MoEModel

model = MoEModel("deepseek-ai/DeepSeek-V3-Base")
```

**Automatically detected for:**
- DeepSeek family (DeepSeek V2, DeepSeek V3, etc.)
- Mixtral family (Mixtral 8x7B, Mixtral 8x22B, etc.)
- Qwen MoE variants (Qwen2.5-MoE, etc.)
- Any model with expert routing mechanisms

**Additional Methods:**

##### `get_expert_metrics() -> Dict[str, Any]`

Get MoE-specific metrics including expert utilization.

**Returns:**
- `Dict[str, Any]`: Expert-specific metrics

**Example:**
```python
moe_model = MoEModel("deepseek-ai/DeepSeek-V3-Base")
expert_metrics = moe_model.get_expert_metrics()
print(f"Active experts per token: {expert_metrics['experts_per_token']}")
print(f"Total experts: {expert_metrics['num_experts']}")
```

##### `compute_memory_requirements()` - MoE-Specific Parameters

MoE models support additional mixed precision parameters for expert components:

**Additional Parameters:**
- `expert_parameter_dtype` (str, optional): Precision for expert parameters (default: 'fp8')

**MoE Mixed Precision Example:**
```python
# Optimize expert memory usage
memory = moe_model.compute_memory_requirements(
    sequence_length=4096,
    weight_dtype='bf16',                    # Base model weights
    activation_dtype='bf16',                # Activations
    kv_cache_dtype='fp8',                   # KV cache compression
    attention_parameter_dtype='bf16',       # Attention params high precision
    expert_parameter_dtype='fp8',           # Expert params compressed
    include_kv_cache=True
)

# Aggressive expert quantization for memory-constrained deployment
aggressive_memory = moe_model.compute_memory_requirements(
    sequence_length=4096,
    expert_parameter_dtype='fp4',           # 4-bit expert parameters
    attention_parameter_dtype='bf16',       # Keep attention quality
    include_kv_cache=True
)
```

## Comparison Module

### Comparator

Engine for comparing multiple models.

```python
from llm_modeling_metrics.comparison import Comparator

comparator = Comparator()
```

#### Methods

##### `compare_models(model_names: List[str], sequence_length: int = 2048, parallel_configs: Optional[List[ParallelConfig]] = None) -> ComparisonResult`

Compare multiple models across metrics.

**Parameters:**
- `model_names` (List[str]): List of model names to compare
- `sequence_length` (int): Sequence length for analysis
- `parallel_configs` (List[ParallelConfig], optional): Parallel configurations to test

**Returns:**
- `ComparisonResult`: Comparison results

**Example:**
```python
results = comparator.compare_models([
    "meta-llama/Llama-2-7b-hf",
    "deepseek-ai/DeepSeek-V3-Base"
])

# Access results
df = results.to_dataframe()
results.export_excel("comparison.xlsx")
```

### ComparisonResult

Container for comparison results.

```python
@dataclass
class ComparisonResult:
    models: List[str]
    metrics: Dict[str, List[Any]]
    parallel_configs: List[ParallelConfig]
    timestamp: datetime
```

#### Methods

##### `to_dataframe() -> pd.DataFrame`

Convert results to pandas DataFrame.

##### `export_excel(filename: str)`

Export results to Excel file.

##### `export_json(filename: str)`

Export results to JSON file.

##### `export_csv(filename: str)`

Export results to CSV file.

## Web Interface

### create_app()

Create FastAPI application for web interface.

```python
from llm_modeling_metrics.web import create_app
import uvicorn

app = create_app()
uvicorn.run(app, host="0.0.0.0", port=8000)
```

### API Endpoints

#### POST `/api/analyze`

Analyze models and return metrics.

**Request Body:**
```json
{
    "model_names": ["meta-llama/Llama-2-7b-hf"],
    "sequence_length": 2048,
    "parallel_config": {
        "tensor_parallel_size": 1
    }
}
```

**Response:**
```json
{
    "results": {
        "meta-llama/Llama-2-7b-hf": {
            "total_params": 6738415616,
            "flops_forward": 1374389248000,
            "memory_params": 26953662464
        }
    },
    "execution_time": 1.23
}
```

#### GET `/api/models/supported`

Get list of supported model architectures.

**Response:**
```json
{
    "architectures": ["llama", "qwen", "mistral", "deepseek", "mixtral"]
}
```

## Error Handling

### Exception Hierarchy

```python
class LLMModelingError(Exception):
    """Base exception for LLM modeling package"""

class ConfigurationError(LLMModelingError):
    """Model configuration is invalid or unavailable"""

class ParallelConfigError(LLMModelingError):
    """Parallel configuration is invalid"""

class ModelNotSupportedError(LLMModelingError):
    """Model architecture is not supported"""

class ComputationError(LLMModelingError):
    """Metric computation failed"""
```

### Error Handling Examples

```python
from llm_modeling_metrics import ModelFactory, ConfigurationError

try:
    model = ModelFactory.create_model("invalid-model-name")
except ConfigurationError as e:
    print(f"Configuration error: {e}")
except ModelNotSupportedError as e:
    print(f"Model not supported: {e}")
```

## Mixed Precision Support

The package provides comprehensive mixed precision support for optimizing memory usage and performance. See the dedicated guides for detailed information:

- **[Mixed Precision Guide](mixed_precision_guide.md)** - Comprehensive guide to mixed precision concepts and usage
- **[Migration Guide](mixed_precision_migration.md)** - Step-by-step migration from single precision to mixed precision
- **[Best Practices](mixed_precision_best_practices.md)** - Production-ready best practices and optimization strategies

### Quick Start with Mixed Precision

```python
# Basic mixed precision configuration
memory = model.compute_memory_requirements(
    sequence_length=2048,
    weight_dtype='bf16',      # High quality weights
    activation_dtype='bf16',  # Stable activations
    kv_cache_dtype='fp8',     # Memory-efficient KV cache
    include_kv_cache=True
)

# MoE-specific mixed precision
moe_memory = moe_model.compute_memory_requirements(
    sequence_length=4096,
    attention_parameter_dtype='bf16',  # Keep attention quality
    expert_parameter_dtype='fp8',      # Compress expert parameters
    include_kv_cache=True
)
```

## Utilities

### Caching

The package includes automatic caching for model configurations and computation results.

```python
from llm_modeling_metrics.utils.caching import CacheManager

cache_manager = CacheManager(cache_dir="./cache")
cache_manager.clear_cache()  # Clear all cached data
cache_manager.get_cache_stats()  # Get cache statistics
```

### Validation

Input validation utilities for ensuring data integrity.

```python
from llm_modeling_metrics.utils.validation import validate_model_name, validate_parallel_config

# Validate model name format
is_valid = validate_model_name("meta-llama/Llama-2-7b-hf")

# Validate parallel configuration
config = ParallelConfig(tensor_parallel_size=4)
is_valid = validate_parallel_config(config, model_config)
```