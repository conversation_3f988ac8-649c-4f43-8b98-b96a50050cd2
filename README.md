# LLM Modeling Metrics

A comprehensive Python library for analyzing computational requirements and performance characteristics of Large Language Models (LLMs), including both dense models and Mixture of Experts (MoE) architectures.

## Features

- **Automatic Model Configuration**: Fetch model configurations from HuggingFace Hub
- **FLOP Computation**: Calculate forward pass FLOPs for various LLM architectures
- **Memory Analysis**: Estimate parameter and activation memory requirements
- **Attention Operators**: Modular support for MHA, GQA, and MLA attention mechanisms
- **Parallel Strategy Analysis**: Analyze tensor parallelism effects on matrix shapes
- **Model Comparison**: Compare multiple models across various metrics
- **Web Interface**: Interactive dashboard for model analysis and comparison
- **Export Capabilities**: Export results to JSON, CSV, and Excel formats

## Supported Models

All models are automatically classified into two types based on their architecture:

### Dense Models
Standard transformer models where all parameters are active for every token:
- **Llama family**: Llama 2, Llama 3, Code Llama, and variants
- **Qwen family**: <PERSON>wen, <PERSON>wen2, Qwen2.5, and variants
- **Mistral family**: Mistral 7B and other dense variants
- **Other dense models**: Any standard transformer architecture

### Mixture of Experts (MoE) Models
Sparse transformer models where only a subset of parameters are active per token:
- **DeepSeek family**: DeepSeek V2, DeepSeek V3, and variants
- **Mixtral family**: Mixtral 8x7B, Mixtral 8x22B, and variants
- **Qwen MoE**: Qwen2.5-MoE and other MoE variants
- **Other MoE models**: Any model with expert routing mechanisms

## Installation

```bash
pip install llm-modeling-metrics
```

For web interface support:
```bash
pip install llm-modeling-metrics[web]
```

For development:
```bash
pip install llm-modeling-metrics[dev]
```

## Quick Start

### Basic Model Analysis

```python
from llm_modeling_metrics import ModelFactory, ParallelConfig

# Create a model instance
model = ModelFactory.create_model("meta-llama/Llama-2-7b-hf")

# Get basic metrics
metrics = model.get_metrics(sequence_length=2048)
print(f"Total parameters: {metrics.total_params:,}")
print(f"Forward pass FLOPs: {metrics.flops_forward:,}")

# Analyze with tensor parallelism
parallel_config = ParallelConfig(tensor_parallel_size=4)
parallel_metrics = model.get_metrics(
    sequence_length=2048, 
    parallel_config=parallel_config
)
```

### Model Comparison

```python
from llm_modeling_metrics.comparison import Comparator

# Compare multiple models
comparator = Comparator()
results = comparator.compare_models([
    "meta-llama/Llama-2-7b-hf",
    "deepseek-ai/DeepSeek-V3-Base",
    "Qwen/Qwen2.5-7B"
])

# Export results
results.export_excel("model_comparison.xlsx")
results.export_json("model_comparison.json")
```

### Web Interface

```python
from llm_modeling_metrics.web import create_app
import uvicorn

app = create_app()
uvicorn.run(app, host="0.0.0.0", port=8000)
```

Then visit `http://localhost:8000` for the interactive dashboard.

## Documentation

- [API Reference](docs/api_reference.md)
- [Attention Operators Guide](docs/attention_operators.md)
- [Tutorial Notebooks](docs/tutorials/)
- [Deployment Guide](docs/deployment.md)
- [Contributing Guide](CONTRIBUTING.md)

## Examples

See the [examples](examples/) directory for comprehensive usage examples:

- [Basic Model Analysis](examples/basic_analysis.py)
- [Attention Operators Comparison](examples/attention_operators_example.py)
- [Parallel Strategy Comparison](examples/parallel_strategies.py)
- [MoE Model Analysis](examples/moe_analysis.py)
- [DeepSeek V3 Analysis](tests/test_deepseek_v3.py)
- [Custom Model Implementation](examples/custom_model.py)

## License

MIT License - see [LICENSE](LICENSE) file for details.

## Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

## Citation

If you use this package in your research, please cite:

```bibtex
@software{llm_modeling_metrics,
  title={LLM Modeling Metrics: A Comprehensive Analysis Library for Large Language Models},
  author={LLM Modeling Metrics Team},
  year={2024},
  url={https://github.com/your-org/llm-modeling-metrics}
}
```