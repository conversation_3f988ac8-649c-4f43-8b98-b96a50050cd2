"""
Pydantic models for API request/response validation.
"""

from pydantic import BaseModel, Field, validator, field_serializer
from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from enum import Enum

from ..core.base_model import ParallelConfig, ModelMetrics
from ..comparison.comparator import ComparisonResult


class PrecisionType(str, Enum):
    """Supported precision types for model analysis."""
    FP32 = "fp32"
    FP16 = "fp16"
    BF16 = "bf16"
    FP8 = "fp8"
    INT8 = "int8"
    INT4 = "int4"


class ParallelConfigModel(BaseModel):
    """Pydantic model for parallel configuration."""
    tensor_parallel_size: int = Field(default=1, ge=1, description="Tensor parallel size")
    pipeline_parallel_size: int = Field(default=1, ge=1, description="Pipeline parallel size")
    data_parallel_size: int = Field(default=1, ge=1, description="Data parallel size")
    expert_parallel_size: int = Field(default=1, ge=1, description="Expert parallel size (for MoE models)")
    expert_data_parallel_size: int = Field(default=1, ge=1, description="Expert data parallel size (for MoE models)")
    
    def to_parallel_config(self) -> ParallelConfig:
        """Convert to core ParallelConfig object."""
        return ParallelConfig(
            tensor_parallel_size=self.tensor_parallel_size,
            pipeline_parallel_size=self.pipeline_parallel_size,
            data_parallel_size=self.data_parallel_size,
            expert_parallel_size=self.expert_parallel_size,
            expert_data_parallel_size=self.expert_data_parallel_size
        )
    
    @classmethod
    def from_parallel_config(cls, config: ParallelConfig) -> 'ParallelConfigModel':
        """Create from core ParallelConfig object."""
        return cls(
            tensor_parallel_size=config.tensor_parallel_size,
            pipeline_parallel_size=config.pipeline_parallel_size,
            data_parallel_size=config.data_parallel_size,
            expert_parallel_size=config.expert_parallel_size,
            expert_data_parallel_size=config.expert_data_parallel_size
        )


class ModelAnalysisRequest(BaseModel):
    """Request model for model analysis endpoint."""
    model_names: List[str] = Field(
        ..., 
        min_items=1, 
        description="List of model names to analyze"
    )
    sequence_length: int = Field(
        default=2048,
        ge=1,
        le=32768,
        description="Input sequence length"
    )
    kv_lens: Optional[int] = Field(
        default=None,
        ge=1,
        le=65536,
        description="KV cache length for decode analysis (defaults to sequence_length if not specified)"
    )
    batch_size: int = Field(
        default=1,
        ge=1,
        le=1024,
        description="Batch size for analysis"
    )
    parallel_config: Optional[ParallelConfigModel] = Field(
        default=None,
        description="Default parallel configuration for all models"
    )
    parallel_configs: Optional[List[ParallelConfigModel]] = Field(
        default=None,
        description="Individual parallel configurations for each model"
    )
    precision: PrecisionType = Field(
        default=PrecisionType.FP16,
        description="Model precision for memory calculations (backward compatibility)"
    )
    # Mixed precision support
    weight_dtype: Optional[str] = Field(
        default=None,
        description="Data type for model weights (fp32, fp16, bf16, int8, fp8, fp4)"
    )
    activation_dtype: Optional[str] = Field(
        default=None,
        description="Data type for activations (fp32, fp16, bf16, int8, fp8)"
    )
    grad_dtype: Optional[str] = Field(
        default=None,
        description="Data type for gradients (fp32, fp16, bf16)"
    )
    optimizer_dtype: Optional[str] = Field(
        default=None,
        description="Data type for optimizer states (fp32, fp16, bf16)"
    )
    kv_cache_dtype: Optional[str] = Field(
        default=None,
        description="Data type for KV cache (fp32, fp16, bf16, int8, fp8)"
    )
    expert_parameter_dtype: Optional[str] = Field(
        default=None,
        description="Data type for expert parameters in MoE models (fp32, fp16, bf16, int8, fp8, fp4)"
    )
    attention_parameter_dtype: Optional[str] = Field(
        default=None,
        description="Data type for attention parameters (fp32, fp16, bf16, int8, fp8)"
    )
    training: bool = Field(
        default=False,
        description="Whether to calculate memory for training (includes gradients/optimizer)"
    )
    include_shapes: bool = Field(
        default=True,
        description="Whether to include matrix shape analysis"
    )
    include_comparison: bool = Field(
        default=True,
        description="Whether to include model comparison results"
    )
    
    @validator('parallel_configs')
    def validate_parallel_configs(cls, v, values):
        """Validate that parallel_configs length matches model_names length."""
        if v is not None and 'model_names' in values:
            if len(v) != len(values['model_names']):
                raise ValueError("Number of parallel configs must match number of models")
        return v
    
    @validator('model_names')
    def validate_model_names(cls, v):
        """Validate model names are not empty."""
        if not v:
            raise ValueError("At least one model name must be provided")
        for name in v:
            if not name.strip():
                raise ValueError("Model names cannot be empty")
        return v
    
    @validator('weight_dtype', 'activation_dtype', 'grad_dtype', 'optimizer_dtype', 'kv_cache_dtype', 'expert_parameter_dtype', 'attention_parameter_dtype')
    def validate_dtype(cls, v):
        """Validate dtype is supported."""
        if v is None:
            return v
        supported_dtypes = ['fp32', 'fp16', 'bf16', 'int8', 'fp8', 'fp4']
        if v not in supported_dtypes:
            raise ValueError(f"Unsupported dtype: {v}. Supported types: {supported_dtypes}")
        return v


class ModelMetricsModel(BaseModel):
    """Pydantic model for model metrics."""
    model_name: str
    architecture: str
    total_params: int
    attention_params: int
    mlp_params: int
    embedding_params: int
    flops_forward: int
    flops_per_token: int
    memory_params: int
    memory_activations: int
    memory_total: int
    attention_shapes: Dict[str, List[int]] = Field(default_factory=dict)
    mlp_shapes: Dict[str, List[int]] = Field(default_factory=dict)
    parallel_config: Optional[ParallelConfigModel] = None
    sequence_length: int
    batch_size: int
    timestamp: datetime
    experts_per_token: Optional[int] = None
    active_params_per_token: Optional[int] = None
    
    @field_serializer('timestamp')
    def serialize_timestamp(self, value: datetime) -> str:
        return value.isoformat()
    
    @classmethod
    def from_model_metrics(cls, metrics: ModelMetrics) -> 'ModelMetricsModel':
        """Create from core ModelMetrics object."""
        # Convert tuple shapes to lists for JSON serialization
        attention_shapes = {
            k: list(v) if isinstance(v, tuple) else v 
            for k, v in metrics.attention_shapes.items()
        }
        mlp_shapes = {
            k: list(v) if isinstance(v, tuple) else v 
            for k, v in metrics.mlp_shapes.items()
        }
        
        return cls(
            model_name=metrics.model_name,
            architecture=metrics.architecture,
            total_params=metrics.total_params,
            attention_params=metrics.attention_params,
            mlp_params=metrics.mlp_params,
            embedding_params=metrics.embedding_params,
            flops_forward=metrics.flops_forward,
            flops_per_token=metrics.flops_per_token,
            memory_params=metrics.memory_params,
            memory_activations=metrics.memory_activations,
            memory_total=metrics.memory_total,
            attention_shapes=attention_shapes,
            mlp_shapes=mlp_shapes,
            parallel_config=ParallelConfigModel.from_parallel_config(metrics.parallel_config) if metrics.parallel_config else None,
            sequence_length=metrics.sequence_length,
            batch_size=metrics.batch_size,
            timestamp=metrics.timestamp,
            experts_per_token=metrics.experts_per_token,
            active_params_per_token=metrics.active_params_per_token
        )


class ComparisonResultModel(BaseModel):
    """Pydantic model for comparison results."""
    models: List[str]
    metrics: Dict[str, List[Any]]
    parallel_configs: List[ParallelConfigModel]
    sequence_length: int
    batch_size: int
    timestamp: datetime
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    @field_serializer('timestamp')
    def serialize_timestamp(self, value: datetime) -> str:
        return value.isoformat()
    
    @classmethod
    def from_comparison_result(cls, result: ComparisonResult) -> 'ComparisonResultModel':
        """Create from core ComparisonResult object."""
        return cls(
            models=result.models,
            metrics=result.metrics,
            parallel_configs=[
                ParallelConfigModel.from_parallel_config(config) 
                for config in result.parallel_configs
            ],
            sequence_length=result.sequence_length,
            batch_size=result.batch_size,
            timestamp=result.timestamp,
            metadata=result.metadata
        )


class ModelAnalysisResponse(BaseModel):
    """Response model for model analysis endpoint."""
    results: Dict[str, ModelMetricsModel] = Field(
        description="Individual model analysis results"
    )
    comparison: Optional[ComparisonResultModel] = Field(
        default=None,
        description="Model comparison results"
    )
    execution_time: float = Field(
        description="Analysis execution time in seconds"
    )
    timestamp: datetime = Field(
        description="Analysis completion timestamp"
    )
    request_id: str = Field(
        description="Unique identifier for this analysis request"
    )
    
    @field_serializer('timestamp')
    def serialize_timestamp(self, value: datetime) -> str:
        return value.isoformat()
    
    @validator('results')
    def validate_results(cls, v):
        """Validate that results are not empty."""
        if not v:
            raise ValueError("Analysis results cannot be empty")
        return v


class ArchitectureInfo(BaseModel):
    """Information about a model architecture."""
    examples: List[str] = Field(description="Example model names for this architecture")
    features: List[str] = Field(description="Key features of this architecture")
    supports_moe: bool = Field(description="Whether this architecture supports Mixture of Experts")


class SupportedModelsResponse(BaseModel):
    """Response model for supported models endpoint."""
    architectures: List[str] = Field(description="List of supported architecture names")
    architecture_info: Dict[str, ArchitectureInfo] = Field(
        description="Detailed information about each architecture"
    )
    total_architectures: int = Field(description="Total number of supported architectures")


class AnalysisStatusEnum(str, Enum):
    """Status values for analysis tasks."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class AnalysisStatus(BaseModel):
    """Status information for analysis tasks."""
    request_id: str
    status: AnalysisStatusEnum
    progress: float = Field(ge=0.0, le=1.0, description="Progress percentage (0.0 to 1.0)")
    message: str = Field(description="Status message")
    created_at: datetime
    updated_at: datetime
    completed_at: Optional[datetime] = None
    error: Optional[str] = None
    result: Optional[ModelAnalysisResponse] = None


class ErrorResponse(BaseModel):
    """Error response model."""
    error: str = Field(description="Error message")
    status_code: int = Field(description="HTTP status code")
    timestamp: datetime = Field(description="Error timestamp")
    details: Optional[str] = Field(default=None, description="Additional error details")
    request_id: Optional[str] = Field(default=None, description="Request ID if available")
    
    @field_serializer('timestamp')
    def serialize_timestamp(self, value: datetime) -> str:
        return value.isoformat()


class ValidationErrorDetail(BaseModel):
    """Detailed validation error information."""
    field: str = Field(description="Field that failed validation")
    message: str = Field(description="Validation error message")
    value: Any = Field(description="Invalid value")


class ValidationErrorResponse(BaseModel):
    """Response model for validation errors."""
    error: str = Field(description="General error message")
    status_code: int = Field(description="HTTP status code")
    timestamp: datetime = Field(description="Error timestamp")
    validation_errors: List[ValidationErrorDetail] = Field(
        description="Detailed validation error information"
    )
    
    @field_serializer('timestamp')
    def serialize_timestamp(self, value: datetime) -> str:
        return value.isoformat()


class HealthResponse(BaseModel):
    """Health check response model."""
    status: str = Field(description="Health status")
    timestamp: datetime = Field(description="Health check timestamp")
    version: str = Field(description="API version")
    uptime: Optional[float] = Field(default=None, description="Uptime in seconds")
    dependencies: Optional[Dict[str, str]] = Field(
        default=None, 
        description="Status of external dependencies"
    )
    
    @field_serializer('timestamp')
    def serialize_timestamp(self, value: datetime) -> str:
        return value.isoformat()


class ModelValidationResponse(BaseModel):
    """Response model for model validation endpoint."""
    valid: bool = Field(description="Whether the model is valid and supported")
    model_name: str = Field(description="Name of the validated model")
    architecture: Optional[str] = Field(default=None, description="Detected architecture")
    supported: bool = Field(description="Whether the architecture is supported")
    config_available: bool = Field(description="Whether model configuration is available")
    basic_info: Optional[Dict[str, Any]] = Field(
        default=None, 
        description="Basic model information if available"
    )
    error: Optional[str] = Field(default=None, description="Error message if validation failed")
    error_type: Optional[str] = Field(default=None, description="Type of error encountered")


# Custom JSON encoders for complex types
# Memory Analysis Models

class MemoryAnalysisRequest(BaseModel):
    """Request model for memory analysis endpoint."""
    model_names: List[str] = Field(
        ..., 
        min_items=1, 
        description="List of model names to analyze"
    )
    sequence_length: int = Field(
        default=2048,
        ge=1,
        le=32768,
        description="Input sequence length"
    )
    kv_lens: Optional[int] = Field(
        default=None,
        ge=1,
        le=65536,
        description="KV cache length for decode analysis (defaults to sequence_length if not specified)"
    )
    batch_size: int = Field(
        default=1,
        ge=1,
        le=1024,
        description="Batch size for analysis"
    )
    dtype: str = Field(
        default='fp16',
        description="Default data type for calculations (backward compatibility)"
    )
    weight_dtype: Optional[str] = Field(
        default=None,
        description="Data type for model weights (fp32, fp16, bf16, int8, fp8, fp4)"
    )
    activation_dtype: Optional[str] = Field(
        default=None,
        description="Data type for activations (fp32, fp16, bf16, int8, fp8)"
    )
    grad_dtype: Optional[str] = Field(
        default=None,
        description="Data type for gradients (fp32, fp16, bf16)"
    )
    optimizer_dtype: Optional[str] = Field(
        default=None,
        description="Data type for optimizer states (fp32, fp16, bf16)"
    )
    kv_cache_dtype: Optional[str] = Field(
        default=None,
        description="Data type for KV cache (fp32, fp16, bf16, int8, fp8)"
    )
    expert_parameter_dtype: Optional[str] = Field(
        default=None,
        description="Data type for expert parameters in MoE models (fp32, fp16, bf16, int8, fp8, fp4)"
    )
    attention_parameter_dtype: Optional[str] = Field(
        default=None,
        description="Data type for attention parameters (fp32, fp16, bf16, int8, fp8)"
    )
    training: bool = Field(
        default=False,
        description="Whether to calculate memory for training (includes gradients/optimizer)"
    )
    include_total_memory: bool = Field(
        default=False,
        description="Whether to include total memory calculations"
    )
    include_kv_cache: bool = Field(
        default=True,
        description="Whether to include KV cache memory calculations"
    )
    
    @validator('dtype', 'weight_dtype', 'activation_dtype', 'grad_dtype', 'optimizer_dtype', 'kv_cache_dtype', 'expert_parameter_dtype', 'attention_parameter_dtype')
    def validate_dtype(cls, v):
        """Validate dtype is supported."""
        if v is None:
            return v
        supported_dtypes = ['fp32', 'fp16', 'bf16', 'int8', 'fp8', 'fp4']
        if v not in supported_dtypes:
            raise ValueError(f"Unsupported dtype: {v}. Supported types: {supported_dtypes}")
        return v
    
    @validator('model_names')
    def validate_model_names(cls, v):
        """Validate model names are not empty."""
        if not v:
            raise ValueError("At least one model name must be provided")
        for name in v:
            if not name.strip():
                raise ValueError("Model names cannot be empty")
        return v


class KVGrowthAnalysisRequest(BaseModel):
    """Request model for KV cache growth analysis endpoint."""
    model_names: List[str] = Field(
        ..., 
        min_items=1, 
        description="List of model names to analyze"
    )
    min_sequence_length: int = Field(
        default=512, 
        ge=1, 
        le=32768, 
        description="Minimum sequence length for analysis"
    )
    max_sequence_length: int = Field(
        default=32768, 
        ge=1, 
        le=65536, 
        description="Maximum sequence length for analysis"
    )
    sequence_length_step: int = Field(
        default=1024, 
        ge=1, 
        le=8192, 
        description="Step size for sequence length increments"
    )
    batch_size: int = Field(
        default=1, 
        ge=1, 
        le=1024, 
        description="Batch size for analysis"
    )
    dtype: str = Field(
        default='fp16',
        description="Default data type for calculations (backward compatibility)"
    )
    kv_cache_dtype: Optional[str] = Field(
        default=None,
        description="Data type for KV cache memory calculations (fp32, fp16, bf16, int8, fp8)"
    )
    
    @validator('dtype', 'kv_cache_dtype')
    def validate_dtype(cls, v):
        """Validate dtype is supported."""
        if v is None:
            return v
        supported_dtypes = ['fp32', 'fp16', 'bf16', 'int8', 'fp8', 'fp4']
        if v not in supported_dtypes:
            raise ValueError(f"Unsupported dtype: {v}. Supported types: {supported_dtypes}")
        return v
    
    @validator('max_sequence_length')
    def validate_sequence_length_range(cls, v, values):
        """Validate max_sequence_length is greater than min_sequence_length."""
        if 'min_sequence_length' in values and v <= values['min_sequence_length']:
            raise ValueError("max_sequence_length must be greater than min_sequence_length")
        return v
    
    @validator('model_names')
    def validate_model_names(cls, v):
        """Validate model names are not empty."""
        if not v:
            raise ValueError("At least one model name must be provided")
        for name in v:
            if not name.strip():
                raise ValueError("Model names cannot be empty")
        return v


class MemoryBreakdown(BaseModel):
    """Memory breakdown information for a model."""
    parameters: int = Field(description="Memory used by model parameters in bytes")
    kv_cache: int = Field(default=0, description="Memory used by KV cache in bytes")
    activations: int = Field(description="Memory used by activations in bytes")
    gradients: Optional[int] = Field(default=None, description="Memory used by gradients in bytes (training only)")
    optimizer_states: Optional[int] = Field(default=None, description="Memory used by optimizer states in bytes (training only)")
    total: int = Field(description="Total memory usage in bytes")
    dtype: str = Field(description="Default data type used for calculations")
    dtypes: Optional[Dict[str, Optional[str]]] = Field(
        default=None, 
        description="Mixed precision data types for different components"
    )
    attention_mechanism: str = Field(description="Attention mechanism type (MHA, GQA, MLA)")
    training: bool = Field(default=False, description="Whether this breakdown is for training")


class MixedPrecisionMemoryBreakdown(BaseModel):
    """Enhanced memory breakdown with mixed precision details."""
    total: int = Field(description="Total memory usage in bytes")
    by_component: Dict[str, int] = Field(description="Memory usage by component (parameters, activations, kv_cache, etc.)")
    by_precision: Dict[str, int] = Field(description="Memory usage by precision type (fp16, fp8, etc.)")
    precision_config: Dict[str, Optional[str]] = Field(description="Precision configuration used")
    efficiency_metrics: Dict[str, float] = Field(description="Memory efficiency metrics")
    attention_mechanism: str = Field(description="Attention mechanism type (MHA, GQA, MLA)")
    training: bool = Field(default=False, description="Whether this breakdown is for training")
    
    @validator('attention_mechanism')
    def validate_attention_mechanism(cls, v):
        """Validate attention mechanism type."""
        valid_mechanisms = ['MHA', 'GQA', 'MLA', 'Unknown']
        if v not in valid_mechanisms:
            raise ValueError(f"Invalid attention mechanism: {v}. Valid types: {valid_mechanisms}")
        return v
    
    @validator('attention_mechanism')
    def validate_attention_mechanism(cls, v):
        """Validate attention mechanism type."""
        valid_mechanisms = ['MHA', 'GQA', 'MLA', 'Unknown']
        if v not in valid_mechanisms:
            raise ValueError(f"Invalid attention mechanism: {v}. Valid types: {valid_mechanisms}")
        return v


class KVGrowthPoint(BaseModel):
    """Data point for KV cache memory growth analysis."""
    sequence_length: int = Field(description="Sequence length for this data point")
    memory_bytes: int = Field(description="KV cache memory usage in bytes")
    memory_human: str = Field(description="Human-readable memory usage (e.g., '1.2 GB')")
    
    @validator('sequence_length')
    def validate_sequence_length(cls, v):
        """Validate sequence length is positive."""
        if v <= 0:
            raise ValueError("Sequence length must be positive")
        return v
    
    @validator('memory_bytes')
    def validate_memory_bytes(cls, v):
        """Validate memory bytes is non-negative."""
        if v < 0:
            raise ValueError("Memory bytes must be non-negative")
        return v


class MemoryAnalysisResponse(BaseModel):
    """Response model for memory analysis endpoint."""
    model_results: Dict[str, MemoryBreakdown] = Field(
        description="Memory breakdown results for each model"
    )
    kv_growth_data: Optional[Dict[str, List[KVGrowthPoint]]] = Field(
        default=None,
        description="KV cache growth data for each model (if requested)"
    )
    execution_time: float = Field(
        description="Analysis execution time in seconds"
    )
    timestamp: datetime = Field(
        description="Analysis completion timestamp"
    )
    request_id: str = Field(
        description="Unique identifier for this analysis request"
    )
    
    @field_serializer('timestamp')
    def serialize_timestamp(self, value: datetime) -> str:
        return value.isoformat()
    
    @validator('model_results')
    def validate_model_results(cls, v):
        """Validate that model results are not empty."""
        if not v:
            raise ValueError("Model results cannot be empty")
        return v
    
    @validator('execution_time')
    def validate_execution_time(cls, v):
        """Validate execution time is non-negative."""
        if v < 0:
            raise ValueError("Execution time must be non-negative")
        return v


class CustomJSONEncoder:
    """Custom JSON encoder for complex types."""
    
    @staticmethod
    def encode_datetime(obj: datetime) -> str:
        """Encode datetime objects."""
        return obj.isoformat()
    
    @staticmethod
    def encode_model_metrics(obj: ModelMetrics) -> Dict[str, Any]:
        """Encode ModelMetrics objects."""
        return ModelMetricsModel.from_model_metrics(obj).dict()
    
    @staticmethod
    def encode_comparison_result(obj: ComparisonResult) -> Dict[str, Any]:
        """Encode ComparisonResult objects."""
        return ComparisonResultModel.from_comparison_result(obj).dict()

# Hardware-related models
class HardwareTypeEnum(str, Enum):
    """Hardware accelerator types."""
    GPU = "gpu"
    NPU = "npu"


class HardwareSpecModel(BaseModel):
    """Pydantic model for hardware specifications."""
    id: str = Field(description="Unique hardware identifier")
    name: str = Field(description="Human-readable hardware name")
    type: HardwareTypeEnum = Field(description="Hardware type (GPU or NPU)")
    architecture: Optional[str] = Field(default=None, description="Hardware architecture")
    form_factor: Optional[str] = Field(default=None, description="Hardware form factor")
    year: Optional[int] = Field(default=None, description="Release year")
    
    # Memory specifications
    memory_size_gb: int = Field(description="Memory size in GB")
    memory_type: Optional[str] = Field(default=None, description="Memory type (HBM3, GDDR6, etc.)")
    memory_bandwidth_gbps: float = Field(description="Memory bandwidth in GB/s")
    l2_cache_mb: Optional[int] = Field(default=None, description="L2 cache size in MB")
    
    # Performance specifications
    peak_flops: Dict[str, Optional[float]] = Field(default_factory=dict, description="Peak FLOPS by precision (TFLOPS)")
    tensor_performance: Dict[str, Optional[float]] = Field(default_factory=dict, description="Tensor performance by precision (TFLOPS)")
    vector_performance: Dict[str, Optional[float]] = Field(default_factory=dict, description="Vector performance by precision (TFLOPS)")
    
    # Physical specifications
    tdp_watts: Optional[int] = Field(default=None, description="Thermal Design Power in watts")
    manufacturing_process: Optional[str] = Field(default=None, description="Manufacturing process node")
    interconnect: Optional[str] = Field(default=None, description="Interconnect technology")
    
    # Derived properties
    supported_precisions: List[str] = Field(default_factory=list, description="List of supported precisions")
    tensor_cores: Optional[int] = Field(default=None, description="Number of tensor cores")
    
    @validator('memory_size_gb')
    def validate_memory_size(cls, v):
        """Validate memory size is positive."""
        if v <= 0:
            raise ValueError("Memory size must be positive")
        return v
    
    @validator('memory_bandwidth_gbps')
    def validate_memory_bandwidth(cls, v):
        """Validate memory bandwidth is positive."""
        if v <= 0:
            raise ValueError("Memory bandwidth must be positive")
        return v


class HardwareListResponse(BaseModel):
    """Response model for hardware list endpoint."""
    gpu: List[HardwareSpecModel] = Field(description="List of available GPUs")
    npu: List[HardwareSpecModel] = Field(description="List of available NPUs")
    total_count: int = Field(description="Total number of hardware devices")
    
    @validator('total_count')
    def validate_total_count(cls, v, values):
        """Validate total count matches actual count."""
        gpu_count = len(values.get('gpu', []))
        npu_count = len(values.get('npu', []))
        expected_count = gpu_count + npu_count
        if v != expected_count:
            raise ValueError(f"Total count {v} doesn't match actual count {expected_count}")
        return v


class ValidationResultModel(BaseModel):
    """Pydantic model for validation results."""
    is_valid: bool = Field(description="Whether validation passed")
    errors: List[str] = Field(default_factory=list, description="List of validation errors")
    warnings: List[str] = Field(default_factory=list, description="List of validation warnings")
    recommendations: List[str] = Field(default_factory=list, description="List of recommendations")
    
    def has_issues(self) -> bool:
        """Check if there are any errors or warnings."""
        return bool(self.errors or self.warnings)


class HardwareValidationRequest(BaseModel):
    """Request model for hardware validation endpoint."""
    hardware_id: str = Field(description="Hardware device identifier")
    operators: List[Dict[str, Any]] = Field(
        default_factory=list,
        description="List of operator configurations to validate"
    )
    
    @validator('hardware_id')
    def validate_hardware_id(cls, v):
        """Validate hardware ID is not empty."""
        if not v.strip():
            raise ValueError("Hardware ID cannot be empty")
        return v


class HardwareValidationResponse(BaseModel):
    """Response model for hardware validation endpoint."""
    hardware_id: str = Field(description="Hardware device identifier")
    validation_result: ValidationResultModel = Field(description="Validation results")
    hardware_found: bool = Field(description="Whether the hardware was found")
    timestamp: datetime = Field(description="Validation timestamp")
    
    @field_serializer('timestamp')
    def serialize_timestamp(self, value: datetime) -> str:
        return value.isoformat()


class WorkloadProfileModel(BaseModel):
    """Pydantic model for workload profiles."""
    model_type: str = Field(description="Model type (dense, moe)")
    batch_size: int = Field(description="Batch size")
    sequence_length: int = Field(description="Sequence length")
    precision_requirements: List[str] = Field(description="Required precisions")
    memory_constraints: Optional[int] = Field(default=None, description="Memory constraints in GB")
    latency_requirements: Optional[float] = Field(default=None, description="Latency requirements in ms")
    throughput_requirements: Optional[float] = Field(default=None, description="Throughput requirements in tokens/s")
    
    @validator('model_type')
    def validate_model_type(cls, v):
        """Validate model type - only dense and moe are supported."""
        valid_types = ['dense', 'moe']
        if v not in valid_types:
            raise ValueError(f"Invalid model type: {v}. Valid types: {valid_types}")
        return v
    
    @validator('batch_size', 'sequence_length')
    def validate_positive_integers(cls, v):
        """Validate positive integers."""
        if v <= 0:
            raise ValueError("Value must be positive")
        return v


class HardwareRecommendationModel(BaseModel):
    """Pydantic model for hardware recommendations."""
    hardware_id: str = Field(description="Hardware device identifier")
    hardware_name: str = Field(description="Hardware device name")
    score: float = Field(description="Compatibility score (0-100)")
    reasons: List[str] = Field(default_factory=list, description="Reasons for recommendation")
    estimated_performance: Optional[float] = Field(default=None, description="Estimated performance in TFLOPS")
    memory_utilization: Optional[float] = Field(default=None, description="Estimated memory utilization percentage")
    cost_effectiveness: Optional[str] = Field(default=None, description="Cost effectiveness rating")
    
    @validator('score')
    def validate_score(cls, v):
        """Validate score is between 0 and 100."""
        if not 0 <= v <= 100:
            raise ValueError("Score must be between 0 and 100")
        return v


# Roofline and Timing Analysis Models

class KneePointModel(BaseModel):
    """Pydantic model for roofline knee points."""
    operational_intensity: float = Field(description="Operational intensity (FLOP/Byte)")
    performance_tflops: float = Field(description="Performance at knee point in TFLOPS")
    precision: str = Field(description="Precision type")
    hardware_id: str = Field(description="Hardware identifier")


class OperatorPointModel(BaseModel):
    """Pydantic model for operator points on roofline."""
    operator_name: str = Field(description="Operator name")
    operational_intensity: float = Field(description="Operational intensity (FLOP/Byte)")
    achieved_performance_tflops: float = Field(description="Achieved performance in TFLOPS")
    utilization_percent: float = Field(description="Hardware utilization percentage")
    is_compute_bound: bool = Field(description="Whether operator is compute-bound")
    precision: str = Field(description="Precision type")
    hardware_id: str = Field(description="Hardware identifier")


class RooflineDataModel(BaseModel):
    """Pydantic model for roofline data."""
    operational_intensity_range: List[float] = Field(description="Operational intensity range")
    performance_curves: Dict[str, Dict[str, List[float]]] = Field(
        description="Performance curves by hardware and precision"
    )
    knee_points: Dict[str, Dict[str, KneePointModel]] = Field(
        description="Knee points by hardware and precision"
    )
    operator_points: List[OperatorPointModel] = Field(
        default_factory=list,
        description="Operator points on roofline"
    )
    hardware_specs: Dict[str, HardwareSpecModel] = Field(
        description="Hardware specifications"
    )


class RooflinePlotDataModel(BaseModel):
    """Pydantic model for single hardware roofline plot data."""
    hardware_id: str = Field(description="Hardware identifier")
    hardware_name: str = Field(description="Hardware name")
    operational_intensity_range: List[float] = Field(description="Operational intensity range")
    roofline_curves: Dict[str, List[float]] = Field(description="Roofline curves by precision")
    knee_points: Dict[str, KneePointModel] = Field(description="Knee points by precision")
    operator_points: List[OperatorPointModel] = Field(
        default_factory=list,
        description="Operator points"
    )


class ComparisonPlotDataModel(BaseModel):
    """Pydantic model for hardware comparison plot data."""
    hardware_platforms: List[str] = Field(description="List of hardware platform IDs")
    operational_intensity_range: List[float] = Field(description="Operational intensity range")
    roofline_data: Dict[str, RooflinePlotDataModel] = Field(description="Roofline data by hardware")
    performance_rankings: Dict[str, List[str]] = Field(
        default_factory=dict,
        description="Performance rankings by operator"
    )
    recommendations: List[str] = Field(default_factory=list, description="Comparison recommendations")


class RooflineGenerateRequest(BaseModel):
    """Request model for roofline generation endpoint."""
    hardware_ids: List[str] = Field(description="List of hardware IDs to include")
    precisions: Optional[List[str]] = Field(
        default=None,
        description="List of precisions to include (default: common precisions)"
    )
    
    @validator('hardware_ids')
    def validate_hardware_ids(cls, v):
        """Validate hardware IDs list is not empty."""
        if not v:
            raise ValueError("Hardware IDs list cannot be empty")
        return v


class OperatorConfigModel(BaseModel):
    """Pydantic model for operator configuration."""
    name: str = Field(description="Operator name")
    type: str = Field(description="Operator type (attention, mlp, etc.)")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Operator parameters")
    precision: Optional[str] = Field(default=None, description="Operator precision")


class RooflinePlotOperatorsRequest(BaseModel):
    """Request model for plotting operators on roofline."""
    operators: List[OperatorConfigModel] = Field(description="List of operators to plot")
    hardware_id: str = Field(description="Hardware ID for roofline")
    
    @validator('operators')
    def validate_operators(cls, v):
        """Validate operators list is not empty."""
        if not v:
            raise ValueError("Operators list cannot be empty")
        return v


class RooflineCompareRequest(BaseModel):
    """Request model for hardware roofline comparison."""
    hardware_ids: List[str] = Field(description="List of hardware IDs to compare")
    operators: Optional[List[OperatorConfigModel]] = Field(
        default=None,
        description="Optional list of operators to plot on all rooflines"
    )
    
    @validator('hardware_ids')
    def validate_hardware_ids(cls, v):
        """Validate hardware IDs list has at least 2 items."""
        if len(v) < 2:
            raise ValueError("At least 2 hardware IDs required for comparison")
        return v


class OperatorTimingModel(BaseModel):
    """Pydantic model for operator timing analysis."""
    operator_name: str = Field(description="Operator name")
    hardware_id: str = Field(description="Hardware identifier")
    compute_time_ms: float = Field(description="Compute time in milliseconds")
    memory_time_ms: float = Field(description="Memory access time in milliseconds")
    execution_time_ms: float = Field(description="Total execution time in milliseconds")
    bottleneck_type: str = Field(description="Bottleneck type (compute or memory)")
    utilization_percent: float = Field(description="Hardware utilization percentage")
    operational_intensity: float = Field(description="Operational intensity (FLOP/Byte)")
    flops: int = Field(description="Number of floating point operations")
    memory_movement_bytes: int = Field(description="Memory movement in bytes")
    precision_overhead_factor: float = Field(description="Mixed precision overhead factor")
    tensor_core_utilization: bool = Field(description="Whether tensor cores are utilized")
    optimization_opportunities: List[str] = Field(
        default_factory=list,
        description="List of optimization opportunities"
    )


class BottleneckAnalysisModel(BaseModel):
    """Pydantic model for bottleneck analysis."""
    compute_bound_operators: List[str] = Field(description="List of compute-bound operators")
    memory_bound_operators: List[str] = Field(description="List of memory-bound operators")
    compute_utilization_avg: float = Field(description="Average compute utilization")
    memory_utilization_avg: float = Field(description="Average memory utilization")
    overall_bottleneck: str = Field(description="Overall bottleneck type")
    recommendations: List[str] = Field(description="Bottleneck recommendations")


class OptimizationSuggestionModel(BaseModel):
    """Pydantic model for optimization suggestions."""
    operator_name: str = Field(description="Operator name")
    suggestion_type: str = Field(description="Type of optimization suggestion")
    description: str = Field(description="Detailed description")
    expected_improvement_percent: float = Field(description="Expected improvement percentage")
    implementation_complexity: str = Field(description="Implementation complexity level")
    hardware_specific: bool = Field(default=True, description="Whether suggestion is hardware-specific")


class TimingComparisonModel(BaseModel):
    """Pydantic model for timing comparison across hardware."""
    operators: List[str] = Field(description="List of operator names")
    hardware_platforms: List[str] = Field(description="List of hardware platform IDs")
    timing_matrix: Dict[str, Dict[str, OperatorTimingModel]] = Field(
        description="Timing matrix by operator and hardware"
    )
    performance_rankings: Dict[str, List[str]] = Field(
        description="Performance rankings by operator"
    )
    recommendations: List[str] = Field(description="Cross-hardware recommendations")


class TimingAnalyzeRequest(BaseModel):
    """Request model for timing analysis endpoint."""
    operators: List[OperatorConfigModel] = Field(description="List of operators to analyze")
    hardware_id: str = Field(description="Hardware ID for analysis")
    
    @validator('operators')
    def validate_operators(cls, v):
        """Validate operators list is not empty."""
        if not v:
            raise ValueError("Operators list cannot be empty")
        return v


class TimingCompareHardwareRequest(BaseModel):
    """Request model for cross-hardware timing comparison."""
    operators: List[OperatorConfigModel] = Field(description="List of operators to compare")
    hardware_ids: List[str] = Field(description="List of hardware IDs to compare")
    
    @validator('operators')
    def validate_operators(cls, v):
        """Validate operators list is not empty."""
        if not v:
            raise ValueError("Operators list cannot be empty")
        return v
    
    @validator('hardware_ids')
    def validate_hardware_ids(cls, v):
        """Validate hardware IDs list has at least 2 items."""
        if len(v) < 2:
            raise ValueError("At least 2 hardware IDs required for comparison")
        return v


# Multi-Hardware Comparison Models

class HardwareComparisonMetricsModel(BaseModel):
    """Pydantic model for hardware comparison metrics."""
    hardware_id: str = Field(description="Hardware identifier")
    hardware_name: str = Field(description="Hardware name")
    peak_performance_tflops: float = Field(description="Peak performance in TFLOPS")
    memory_bandwidth_gbps: float = Field(description="Memory bandwidth in GB/s")
    memory_size_gb: int = Field(description="Memory size in GB")
    cost_performance_ratio: Optional[float] = Field(default=None, description="Cost per TFLOP")
    power_efficiency: Optional[float] = Field(default=None, description="TFLOPS per Watt")
    memory_efficiency: Optional[float] = Field(default=None, description="GB/s per GB")
    tensor_core_capable: bool = Field(description="Whether hardware has tensor cores")
    supported_precisions: List[str] = Field(description="List of supported precisions")
    architecture_generation: Optional[str] = Field(default=None, description="Architecture generation")


class WorkloadPerformanceAnalysisModel(BaseModel):
    """Pydantic model for workload performance analysis."""
    workload_name: str = Field(description="Workload name")
    hardware_rankings: List[str] = Field(description="Hardware IDs ranked by performance")
    performance_scores: Dict[str, float] = Field(description="Performance scores by hardware")
    bottleneck_analysis: Dict[str, str] = Field(description="Bottleneck type by hardware")
    optimization_opportunities: Dict[str, List[str]] = Field(description="Optimization opportunities by hardware")
    cost_effectiveness: Dict[str, float] = Field(description="Cost effectiveness by hardware")


class HardwareRecommendationEngineModel(BaseModel):
    """Pydantic model for hardware recommendation engine."""
    recommended_hardware: List[str] = Field(description="Ordered list of recommended hardware IDs")
    recommendation_reasons: Dict[str, List[str]] = Field(description="Recommendation reasons by hardware")
    use_case_suitability: Dict[str, Dict[str, float]] = Field(description="Use case suitability scores")
    migration_recommendations: List[str] = Field(description="Migration recommendations")
    cost_analysis: Optional[Dict[str, Any]] = Field(default=None, description="Cost analysis data")


class MultiHardwareComparisonRequest(BaseModel):
    """Request model for multi-hardware comparison."""
    hardware_ids: List[str] = Field(description="List of hardware IDs to compare")
    workload_profiles: Optional[List[WorkloadProfileModel]] = Field(
        default=None,
        description="Optional workload profiles for analysis"
    )
    operators: Optional[List[OperatorConfigModel]] = Field(
        default=None,
        description="Optional operators for detailed timing analysis"
    )
    include_cost_analysis: bool = Field(default=True, description="Whether to include cost analysis")
    
    @validator('hardware_ids')
    def validate_hardware_ids(cls, v):
        """Validate hardware IDs list has at least 2 items."""
        if len(v) < 2:
            raise ValueError("At least 2 hardware IDs required for comparison")
        return v


class MultiHardwareComparisonResponse(BaseModel):
    """Response model for multi-hardware comparison."""
    hardware_platforms: List[str] = Field(description="List of hardware platform IDs")
    comparison_metrics: Dict[str, HardwareComparisonMetricsModel] = Field(
        description="Comparison metrics by hardware"
    )
    workload_analysis: Dict[str, WorkloadPerformanceAnalysisModel] = Field(
        description="Workload analysis by workload type"
    )
    roofline_comparison: ComparisonPlotDataModel = Field(description="Roofline comparison data")
    timing_comparison: Optional[TimingComparisonModel] = Field(
        default=None,
        description="Timing comparison data"
    )
    recommendation_engine: HardwareRecommendationEngineModel = Field(
        description="Hardware recommendation engine results"
    )
    summary_insights: List[str] = Field(description="Summary insights from comparison")
    timestamp: str = Field(description="Analysis timestamp")


class HardwareSelectionWizardRequest(BaseModel):
    """Request model for hardware selection wizard."""
    budget: Optional[float] = Field(default=None, description="Budget constraint in USD")
    use_case: str = Field(description="Primary use case (training, inference, research, production)")
    memory_requirement_gb: Optional[int] = Field(default=None, description="Memory requirement in GB")
    precision_requirements: List[str] = Field(
        default_factory=lambda: ["fp16"],
        description="Required precision support"
    )
    performance_priority: str = Field(
        default="balanced",
        description="Performance priority (compute, memory, balanced)"
    )
    power_constraint_watts: Optional[int] = Field(default=None, description="Power constraint in Watts")
    form_factor_preference: Optional[str] = Field(default=None, description="Form factor preference")
    
    @validator('use_case')
    def validate_use_case(cls, v):
        """Validate use case is supported."""
        valid_cases = ["training", "inference", "research", "production", "general"]
        if v not in valid_cases:
            raise ValueError(f"Use case must be one of: {valid_cases}")
        return v
    
    @validator('performance_priority')
    def validate_performance_priority(cls, v):
        """Validate performance priority is supported."""
        valid_priorities = ["compute", "memory", "balanced", "cost", "efficiency"]
        if v not in valid_priorities:
            raise ValueError(f"Performance priority must be one of: {valid_priorities}")
        return v


class HardwareRecommendationWizardModel(BaseModel):
    """Pydantic model for wizard hardware recommendation."""
    rank: int = Field(description="Recommendation rank")
    hardware_id: str = Field(description="Hardware identifier")
    hardware_name: str = Field(description="Hardware name")
    score: float = Field(description="Recommendation score")
    cost: Optional[float] = Field(default=None, description="Hardware cost in USD")
    reasons: List[str] = Field(description="Reasons for recommendation")
    pros: List[str] = Field(description="Advantages of this hardware")
    cons: List[str] = Field(description="Disadvantages or limitations")


class SelectionGuidanceModel(BaseModel):
    """Pydantic model for selection guidance."""
    summary: str = Field(description="Summary of recommendations")
    key_considerations: List[str] = Field(description="Key considerations for selection")
    next_steps: List[str] = Field(description="Recommended next steps")
    alternatives: List[str] = Field(description="Alternative options or suggestions")


class HardwareSelectionWizardResponse(BaseModel):
    """Response model for hardware selection wizard."""
    recommendations: List[HardwareRecommendationWizardModel] = Field(
        description="List of hardware recommendations"
    )
    guidance: SelectionGuidanceModel = Field(description="Selection guidance")
    total_options: int = Field(description="Total number of hardware options considered")
    filtered_count: int = Field(description="Number of options after filtering")
    requirements_met: bool = Field(description="Whether any hardware meets requirements")


class CrossPlatformTimingRequest(BaseModel):
    """Request model for cross-platform timing analysis."""
    hardware_ids: List[str] = Field(description="List of hardware IDs to compare")
    operators: List[OperatorConfigModel] = Field(description="List of operators to analyze")
    
    @validator('hardware_ids')
    def validate_hardware_ids(cls, v):
        """Validate hardware IDs list has at least 2 items."""
        if len(v) < 2:
            raise ValueError("At least 2 hardware IDs required for comparison")
        return v
    
    @validator('operators')
    def validate_operators(cls, v):
        """Validate operators list is not empty."""
        if not v:
            raise ValueError("Operators list cannot be empty")
        return v


class PerformanceScalingAnalysisModel(BaseModel):
    """Pydantic model for performance scaling analysis."""
    fastest_hardware: str = Field(description="Hardware ID with fastest performance")
    scaling_factors: Dict[str, float] = Field(description="Scaling factors relative to fastest")
    performance_range: Dict[str, float] = Field(description="Performance range (min/max times)")


class CrossPlatformTimingResponse(BaseModel):
    """Response model for cross-platform timing analysis."""
    hardware_platforms: List[str] = Field(description="List of hardware platform IDs")
    timing_comparison: TimingComparisonModel = Field(description="Timing comparison results")
    scaling_analysis: Dict[str, PerformanceScalingAnalysisModel] = Field(
        description="Performance scaling analysis by operator"
    )
    migration_recommendations: List[str] = Field(description="Hardware migration recommendations")
    summary_statistics: Dict[str, Any] = Field(description="Summary statistics")